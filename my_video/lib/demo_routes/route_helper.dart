import 'package:ams/app_imports.dart';

class RouteHelper {
  // ===================== Main Routes =====================
  static const String initial = '/';
  static const String loginPage = '/loginPage';
  static const String forgotPasswordPage = '/forgotPasswordPage';
  static const String signupPage = '/signupPage';
  static const String noInternet = '/noInternet';
  static const String changePasswordPage = '/changePassword';
  static const String signupPageHelper = '/signupPageHelper';
  static const String inspectionReportPage = '/inspectionReportPage';

  // ===================== Super admin main Routes =====================
  static const String superAdminDashboardPage = '/superAdminDashboardPage';
  static const String superAdminApplicationReceived = '/superAdminApplicationReceived';
  static const String superAdminEmpanelledAgencies = '/superAdminEmpanelledAgencies';
  static const String superAdminAuditsDone = '/superAdminAuditsDone';
  static const String superAdminEnlistedAuditors = '/superAdminEnlistedAuditors';
  static const String superAdminRejectedAgencies = '/superAdminRejectedAgencies';
  static const String superAdminReports = '/superAdminReports';

  // ===================== Super admin sub Routes =====================

  static const String superAdminViewAgencyDetails = 'superAdminViewAgencyDetails';
  static const String superAdminViewAuditors = '/superAdminViewAuditors';
  static const String superAdminViewAgencyAuditors = 'superAdminViewAgencyAuditors';
  static const String superAdminAuditReport = 'superAdminAuditReport';
  static const String superAdminExperience = '/superAdminExperience';
  static const String superAdminQualification = '/superAdminQualification';
  static const String superAdminAuditConductByAuditor = 'superAdminAuditConductByAuditor';

  // ==================== Agency Routes =====================
  static const String agencyDashboardPage = '/agencyDashboardPage';
  static const String agencyYourAuditorPage = '/yourAuditorPage';
  static const String experiencePage = '/experiencePage';
  static const String qualificationPage = '/qualificationPage';
  static const String auditReportPage = '/auditReportPage';
  static const String personalInfoPage = '/personalInfoPage';
  static const String assignAuditPage = '/assignAuditPage';
  static const String registerAuditorPage = '/registerAuditorPage';
  static const String agencyCancelledAuditPage = '/agencyCancelledAuditPage';
  static const String pendingWithAuditorPage = '/pendingWithAuditorPage';
  static const String agencyApprovedPage = '/agencyApprovedPage';
  static const String applicationReceivedPage = '/applicationReceivedPage';
  static const String agencyInspectionReportPage = '/agencyInspectionReportPage';
  static const String agencyNonCompliancePage = '/agencyNonCompliancePage';

  // ==================== Auditor Routes =====================
  static const String auditorDashboardPage = '/auditorDashboardPage';
  static const String auditorPersonalInfoPage = '/auditorPersonalInfoPage';
  static const String auditorApprovedAuditPage = '/auditorApprovedAuditPage';
  static const String auditorApprovalPendingAuditPage = '/auditorApprovalPendingAuditPage';
  static const String auditorCancelledAuditPage = '/auditorCancelledAuditPage';
  static const String auditorReceivedAuditPage = '/auditorReceivedAuditPage';
  static const String auditorNonCompliancePage = '/auditorNonCompliancePage';

  // ==================== Sub Routes =====================
  static const String uploadDocumentPage = 'uploadDocumentPage';
  static const String conductAuditPage = 'conductAuditPage';
  static const String checklistPage = 'checklistPage';
  static const String auditorAuditModificationPage = 'auditorAuditModificationPage';
}

class GoRouterObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    logs('MyTest didPush: $route');
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    logs('MyTest didPop: $route');
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    logs('MyTest didRemove: $route');
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    logs('MyTest didReplace: $newRoute');
  }
}

final _rootNavigatorKey = GlobalKey<NavigatorState>();

Future<String?> _redirect(BuildContext context, GoRouterState state) async {
  bool isLogin = await getPrefBoolValue(AppPrefConstants.isLogin);
  int? loginId = await getPrefIntValue(AppPrefConstants.profileId) ;
  if (isLogin && state.uri.path == RouteHelper.loginPage && loginId!=null) {
    if(loginId==1){
      return RouteHelper.superAdminDashboardPage;
    }else if(loginId==2){
      return RouteHelper.agencyDashboardPage;
    }else{
      return RouteHelper.auditorDashboardPage;
    }
  }
  logs('_redirect -> ${state.uri.path}');
  return state.uri.path; // initially it was '/' then path
}

final router = GoRouter(
  redirect: _redirect,
  initialLocation: RouteHelper.initial,
  navigatorKey: _rootNavigatorKey,
  observers: [
    GoRouterObserver(),
  ],
  routes: [
    GoRoute(
      path: RouteHelper.initial,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SplashPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.loginPage,
      pageBuilder: (context, state) {
        Map<String, dynamic> extra = state.extra as Map<String, dynamic>;
        return NoTransitionPage(
          child: LoginPage(message: extra['message']),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.forgotPasswordPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: ForgotPasswordPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminDashboardPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SuperAdminDashboardPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.signupPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SignUpPage(),
        );
      },
      routes: [
        GoRoute(
          path: RouteHelper.uploadDocumentPage,
          pageBuilder: (context, state) {
            Map<String, dynamic> args = state.extra as Map<String, dynamic>;
            return NoTransitionPage(
              child: UploadDocumentPage(
                agencyRegistrationModel: args['agencyRegistrationModel'],
              ),
            );
          },
        ),
      ],
    ),
    GoRoute(
      path: RouteHelper.agencyDashboardPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AgencyDashboardPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.agencyNonCompliancePage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AgencyNonCompliancePage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminApplicationReceived,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SuperAdminApplicationReceivedPage(),
        );
      },
      routes: [
        GoRoute(
          path: RouteHelper.superAdminViewAgencyDetails,
          pageBuilder: (context, state) {
            Map<String, dynamic> args = state.extra as Map<String, dynamic>;
            return NoTransitionPage(
              child: SuperAdminViewAgencyDetailPage(agencyId: args['agencyId']),
            );
          },
        ),
        GoRoute(
          path: RouteHelper.superAdminViewAgencyAuditors,
          pageBuilder: (context, state) {
            Map<String, dynamic> args = state.extra as Map<String, dynamic>;
            return NoTransitionPage(
              child: SuperAdminViewAuditorPage(agencyId: args['agencyId']),
            );
          },
          routes: [
            GoRoute(
              path: RouteHelper.superAdminAuditReport,
              pageBuilder: (context, state) {
                return const NoTransitionPage(
                  child: ConductAuditReportPage(),
                );
              },
            ),
          ],
        ),
      ],
    ),
    GoRoute(
      path: RouteHelper.agencyYourAuditorPage,
      pageBuilder: (context, state) {
        Map<String, dynamic> args = state.extra as Map<String, dynamic>;
        return NoTransitionPage(
          child: YourAuditorPage(agencyId: args['agencyId']),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.auditorDashboardPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorDashboardPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.auditorPersonalInfoPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorPersonalInfoPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.noInternet,
      onExit: (context) async {
        bool isConnected = await ConnectivityHelper.instance.isConnectNetworkWithMessage();
        return isConnected;
      },
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: NoInterNetConnectionPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.changePasswordPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: ChangePasswordPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.auditorApprovedAuditPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorApprovedAuditPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.auditReportPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditReportPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.qualificationPage,
      pageBuilder: (context, state) {
        Map<String, dynamic> args = state.extra as Map<String, dynamic>;
        return NoTransitionPage(
          child: QualificationPage(auditor: args['auditor']),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.experiencePage,
      pageBuilder: (context, state) {
        Map<String, dynamic> args = state.extra as Map<String, dynamic>;
        return NoTransitionPage(
          child: ExperiencePage(auditor: args['auditor']),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.inspectionReportPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: InspectionReportPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminEmpanelledAgencies,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SuperAdminEmpanelledAgenciesPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminViewAuditors,
      pageBuilder: (context, state) {
        Map<String, dynamic> args = state.extra as Map<String, dynamic>;
        return NoTransitionPage(
          child: SuperAdminViewAuditorsPage(
            agencyId: args['agencyId'],
          ),
        );
      },
      routes: [
        GoRoute(
          path: RouteHelper.superAdminAuditConductByAuditor,
          pageBuilder: (context, state) {
            List<AuditModel> extra = state.extra as List<AuditModel>;
            return NoTransitionPage(
              child: SuperAdminAuditConductByAuditorPage(
                audits: extra,
              ),
            );
          },
        ),
      ],
    ),
    GoRoute(
      path: RouteHelper.auditorApprovalPendingAuditPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorApprovalPendingAuditPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.auditorCancelledAuditPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorCancelledAuditPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.auditorReceivedAuditPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorReceivedAuditPage(),
        );
      },
      routes: [
        GoRoute(
          path: RouteHelper.conductAuditPage,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: ConductAuditPage(),
            );
          },
          routes: [
            GoRoute(
              path: RouteHelper.checklistPage,
              pageBuilder: (context, state) {
                return const NoTransitionPage(
                  child: ChecklistPage(),
                );
              },
            ),
          ],
        ),
        GoRoute(
          path: RouteHelper.auditorAuditModificationPage,
          pageBuilder: (context, state) {
            return const NoTransitionPage(
              child: AuditorAuditModificationPage(),
            );
          },
        ),
      ],
    ),
    GoRoute(
      path: RouteHelper.personalInfoPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AgencyPersonalInfoPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.assignAuditPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AssignAuditPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.registerAuditorPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: RegisterAuditorPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminAuditsDone,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SuperAdminAuditsDonePage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.pendingWithAuditorPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: PendingWithAuditorPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminEnlistedAuditors,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SuperAdminEnlistedAuditorsPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminExperience,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorExperiencePage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminQualification,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorQualificationPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.agencyCancelledAuditPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AgencyCancelledAuditPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminRejectedAgencies,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SuperAdminRejectedAgenciesPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.superAdminReports,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: SuperAdminReportsPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.agencyApprovedPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AgencyApprovedPage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.applicationReceivedPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: ApplicationReceivedPage(),
        );
      },
    ),

    GoRoute(
      path: RouteHelper.auditorNonCompliancePage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AuditorNonCompliancePage(),
        );
      },
    ),
    GoRoute(
      path: RouteHelper.agencyInspectionReportPage,
      pageBuilder: (context, state) {
        return const NoTransitionPage(
          child: AgencyInspectionReportPage(),
        );
      },
    ),
  ],
);
