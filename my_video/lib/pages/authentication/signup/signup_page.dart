import 'package:my_video/app_imports.dart';

class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  State<SignupPage> createState() => SignupPageState();
}

class SignupPageState extends State<SignupPage> {
  SignupPageHelper? _signupPageHelper;
  late AuthenticationController authController;

  @override
  void dispose() {
    _signupPageHelper?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    _signupPageHelper = _signupPageHelper ?? SignupPageHelper(this);

    return GetBuilder(
      init: AuthenticationController(),
      builder: (AuthenticationController controller) {
        authController = controller;
        return AppScaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(MySize.width(16)),
              child: Form(
                key: _signupPageHelper!.signup<PERSON><PERSON><PERSON><PERSON>,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Space.height(40),

                    // Logo and Title
                    Column(
                      children: [
                        Container(
                          width: MySize.width(80),
                          height: MySize.height(80),
                          decoration: BoxDecoration(
                            color: AppColorConstants.primaryColor,
                            borderRadius: BorderRadius.circular(
                              MySize.radius(20),
                            ),
                          ),
                          child: Icon(
                            Icons.movie_outlined,
                            size: MySize.height(40),
                            color: AppColorConstants.textPrimary,
                          ),
                        ),

                        Space.height(24),

                        AppText(
                          text: 'Create Account',
                          fontSize: MySize.fontSize(28),
                          fontWeight: FontWeight.bold,
                          color: AppColorConstants.textPrimary,
                          textAlign: TextAlign.center,
                        ),

                        Space.height(8),

                        AppText(
                          text: 'Join us to enjoy unlimited movies',
                          fontSize: MySize.fontSize(16),
                          color: AppColorConstants.textSecondary,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),

                    Space.height(40),

                    // First Name Field
                    AppTextFormField(
                      controller: _signupPageHelper!.firstNameController,
                      labelText: 'First Name',
                      hintText: 'Enter your first name',
                      keyboardType: TextInputType.name,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.person_outline,
                        size: MySize.height(20),
                      ),
                      validator: _signupPageHelper!.validateFirstName,
                    ),

                    Space.height(16),

                    // Last Name Field (Optional)
                    AppTextFormField(
                      controller: _signupPageHelper!.lastNameController,
                      labelText: 'Last Name (Optional)',
                      hintText: 'Enter your last name',
                      keyboardType: TextInputType.name,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.person_outline,
                        size: MySize.height(20),
                      ),
                      validator: _signupPageHelper!.validateLastName,
                    ),

                    Space.height(16),

                    // Gender Selection
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppText(
                          text: 'Gender',
                          fontSize: MySize.fontSize(16),
                          fontWeight: FontWeight.w500,
                          color: AppColorConstants.textPrimary,
                        ),
                        Space.height(8),
                        Row(
                          children: [
                            Expanded(
                              child: RadioListTile<String>(
                                title: const AppText(text: 'Male'),
                                value: 'm',
                                groupValue: _signupPageHelper!.selectedGender,
                                onChanged: (value) =>
                                    _signupPageHelper!.setGender(value!),
                                activeColor: AppColorConstants.primaryColor,
                              ),
                            ),
                            Expanded(
                              child: RadioListTile<String>(
                                title: const AppText(text: 'Female'),
                                value: 'f',
                                groupValue: _signupPageHelper!.selectedGender,
                                onChanged: (value) =>
                                    _signupPageHelper!.setGender(value!),
                                activeColor: AppColorConstants.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    Space.height(16),

                    // Email Field
                    AppTextFormField(
                      controller: _signupPageHelper!.emailController,
                      labelText: 'Email',
                      hintText: 'Enter your email',
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.email_outlined,
                        size: MySize.height(20),
                      ),
                      validator: _signupPageHelper!.validateEmail,
                    ),

                    Space.height(16),

                    // Password Field
                    AppTextFormField(
                      controller: _signupPageHelper!.passwordController,
                      labelText: 'Password',
                      hintText: 'Enter your password',
                      obscureText: !_signupPageHelper!.isPasswordVisible,
                      textInputAction: TextInputAction.next,
                      prefixIcon: Icon(
                        Icons.lock_outlined,
                        size: MySize.height(20),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _signupPageHelper!.isPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                          size: MySize.height(20),
                        ),
                        onPressed: _signupPageHelper!.togglePasswordVisibility,
                      ),
                      validator: _signupPageHelper!.validatePassword,
                    ),

                    Space.height(16),

                    // Confirm Password Field
                    AppTextFormField(
                      controller: _signupPageHelper!.confirmPasswordController,
                      labelText: 'Confirm Password',
                      hintText: 'Confirm your password',
                      obscureText: !_signupPageHelper!.isConfirmPasswordVisible,
                      textInputAction: TextInputAction.done,
                      prefixIcon: Icon(
                        Icons.lock_outlined,
                        size: MySize.height(20),
                      ),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _signupPageHelper!.isConfirmPasswordVisible
                              ? Icons.visibility_off
                              : Icons.visibility,
                          size: MySize.height(20),
                        ),
                        onPressed:
                            _signupPageHelper!.toggleConfirmPasswordVisibility,
                      ),
                      validator: _signupPageHelper!.validateConfirmPassword,
                    ),

                    Space.height(24),

                    // Terms and Conditions
                    Row(
                      children: [
                        Checkbox(
                          value: _signupPageHelper!.acceptTerms,
                          onChanged: _signupPageHelper!.toggleAcceptTerms,
                          activeColor: AppColorConstants.primaryColor,
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () => _signupPageHelper!.toggleAcceptTerms(),
                            child: RichText(
                              text: TextSpan(
                                style: TextStyle(
                                  fontSize: MySize.fontSize(14),
                                  color: AppColorConstants.textSecondary,
                                ),
                                children: [
                                  const TextSpan(text: 'I agree to the '),
                                  TextSpan(
                                    text: 'Terms of Service',
                                    style: TextStyle(
                                      color: AppColorConstants.primaryColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap =
                                          _signupPageHelper!.showTermsOfService,
                                  ),
                                  const TextSpan(text: ' and '),
                                  TextSpan(
                                    text: 'Privacy Policy',
                                    style: TextStyle(
                                      color: AppColorConstants.primaryColor,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap =
                                          _signupPageHelper!.showPrivacyPolicy,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    Space.height(32),

                    // Signup Button
                    _signupPageHelper!.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : AppButton(
                            text: 'Create Account',
                            onPressed: _signupPageHelper!.signup,
                            isLoading: _signupPageHelper!.isLoading,
                            backgroundColor: AppColorConstants.primaryColor,
                            textColor: AppColorConstants.textPrimary,
                          ),

                    Space.height(24),

                    // Login Link
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppText(
                          text: "Already have an account? ",
                          fontSize: MySize.fontSize(14),
                          color: AppColorConstants.textSecondary,
                        ),
                        GestureDetector(
                          onTap: _signupPageHelper!.navigateToLogin,
                          child: AppText(
                            text: 'Sign In',
                            fontSize: MySize.fontSize(14),
                            color: AppColorConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),

                    Space.height(40),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
