import 'package:my_video/app_imports.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isEditing = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    // Load user data from storage
    _nameController.text =
        HiveHelper.getSetting<String>(
          'user_name',
          defaultValue: 'Movie Lover',
        ) ??
        'Movie Lover';
    _emailController.text =
        HiveHelper.getSetting<String>(
          'user_email',
          defaultValue: '<EMAIL>',
        ) ??
        '<EMAIL>';
    _phoneController.text =
        HiveHelper.getSetting<String>('user_phone', defaultValue: '') ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'Profile',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColorConstants.textPrimary,
          ),
          onPressed: gotoBack,
        ),
        actions: [
          if (!_isEditing)
            TextButton(
              onPressed: () => setState(() => _isEditing = true),
              child: const AppText(
                text: 'Edit',
                color: AppColorConstants.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          // Logout button for testing
          IconButton(
            onPressed: () async {
              try {
                final authController = Get.find<AuthenticationController>();
                await authController.logoutFromAPI();

                // Clear local storage
                await AppSharedPreference.logout();

                AppHelper.showToast('logout_successful');

                // Navigate to login
                gotoLoginPage();
              } catch (e) {
                AppHelper.logError('Logout error', e);
                AppHelper.showToast('something_went_wrong', isError: true);
              }
            },
            icon: const Icon(Icons.logout, color: AppColorConstants.colorRed),
            tooltip: 'Logout',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(MySize.width(16)),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // Profile Picture Section
              _buildProfilePicture(),
              Space.height(32),

              // User Information Form
              _buildUserForm(),
              Space.height(32),

              // Action Buttons
              if (_isEditing) _buildEditingButtons(),
              if (!_isEditing) _buildProfileActions(),

              Space.height(100), // Bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfilePicture() {
    return Column(
      children: [
        Stack(
          children: [
            CircleAvatar(
              radius: MySize.height(60),
              backgroundColor: AppColorConstants.primaryColor,
              child: AppText(
                text: _nameController.text.isNotEmpty
                    ? _nameController.text[0].toUpperCase()
                    : 'U',
                fontSize: MySize.fontSize(36),
                fontWeight: FontWeight.bold,
                color: AppColorConstants.textPrimary,
              ),
            ),
            if (_isEditing)
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: _changeProfilePicture,
                  child: Container(
                    padding: EdgeInsets.all(MySize.height(8)),
                    decoration: BoxDecoration(
                      color: AppColorConstants.primaryColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColorConstants.backgroundColor,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.camera_alt,
                      color: AppColorConstants.textPrimary,
                      size: MySize.height(16),
                    ),
                  ),
                ),
              ),
          ],
        ),
        Space.height(16),
        AppText(
          text: _nameController.text,
          fontSize: MySize.fontSize(24),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        AppText(
          text: _emailController.text,
          fontSize: MySize.fontSize(14),
          color: AppColorConstants.textSecondary,
        ),
      ],
    );
  }

  Widget _buildUserForm() {
    return Column(
      children: [
        _buildTextField(
          controller: _nameController,
          label: 'Full Name',
          icon: Icons.person_outline,
          enabled: _isEditing,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your name';
            }
            return null;
          },
        ),
        Space.height(16),

        _buildTextField(
          controller: _emailController,
          label: 'Email Address',
          icon: Icons.email_outlined,
          enabled: _isEditing,
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email';
            }
            if (!GetUtils.isEmail(value)) {
              return 'Please enter a valid email';
            }
            return null;
          },
        ),
        Space.height(16),

        _buildTextField(
          controller: _phoneController,
          label: 'Phone Number',
          icon: Icons.phone_outlined,
          enabled: _isEditing,
          keyboardType: TextInputType.phone,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool enabled = true,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: label,
          fontSize: MySize.fontSize(16),
          fontWeight: FontWeight.w600,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(8),
        TextFormField(
          controller: controller,
          enabled: enabled,
          keyboardType: keyboardType,
          validator: validator,
          decoration: InputDecoration(
            prefixIcon: Icon(
              icon,
              color: enabled
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.textSecondary,
            ),
            filled: true,
            fillColor: enabled
                ? AppColorConstants.cardColor
                : AppColorConstants.surfaceColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.dividerColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.dividerColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.primaryColor),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MySize.radius(8)),
              borderSide: BorderSide(color: AppColorConstants.dividerColor),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEditingButtons() {
    return Row(
      children: [
        Expanded(
          child: AppButton(
            text: 'Cancel',
            onPressed: _cancelEditing,
            backgroundColor: AppColorConstants.cardColor,
            textColor: AppColorConstants.textSecondary,
          ),
        ),
        Space.width(16),
        Expanded(
          child: AppButton(
            text: _isLoading ? 'Saving...' : 'Save Changes',
            onPressed: _isLoading ? null : _saveProfile,
            icon: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColorConstants.textPrimary,
                    ),
                  )
                : const Icon(Icons.save, color: AppColorConstants.textPrimary),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileActions() {
    return Column(
      children: [
        _buildActionTile(
          icon: Icons.history,
          title: 'Watch History',
          subtitle: 'View your recently watched videos',
          onTap: () => _showComingSoon('Watch History'),
        ),
        Space.height(12),

        _buildActionTile(
          icon: Icons.favorite_outline,
          title: 'Favorites',
          subtitle: 'Your liked videos and movies',
          onTap: () => _showComingSoon('Favorites'),
        ),
        Space.height(12),

        _buildActionTile(
          icon: Icons.download_outlined,
          title: 'Downloads',
          subtitle: 'Manage your offline content',
          onTap: () => _showComingSoon('Downloads'),
        ),
        Space.height(12),

        _buildActionTile(
          icon: Icons.notifications_outlined,
          title: 'Notification Preferences',
          subtitle: 'Customize your notifications',
          onTap: () => _showComingSoon('Notification Preferences'),
        ),
        Space.height(12),

        _buildActionTile(
          icon: Icons.security_outlined,
          title: 'Privacy & Security',
          subtitle: 'Manage your privacy settings',
          onTap: () => _showComingSoon('Privacy & Security'),
        ),
      ],
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(8)),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: AppColorConstants.primaryColor,
          size: MySize.height(24),
        ),
        title: AppText(
          text: title,
          fontSize: MySize.fontSize(16),
          fontWeight: FontWeight.w600,
          color: AppColorConstants.textPrimary,
        ),
        subtitle: AppText(
          text: subtitle,
          fontSize: MySize.fontSize(14),
          color: AppColorConstants.textSecondary,
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: AppColorConstants.textSecondary,
          size: MySize.height(16),
        ),
        onTap: onTap,
      ),
    );
  }

  void _changeProfilePicture() {
    Get.snackbar(
      'Coming Soon',
      'Profile picture upload will be available soon',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
    );
  }

  void _cancelEditing() {
    setState(() {
      _isEditing = false;
    });
    _loadUserData(); // Reset form data
  }

  void _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Save user data
        await HiveHelper.saveSetting('user_name', _nameController.text);
        await HiveHelper.saveSetting('user_email', _emailController.text);
        await HiveHelper.saveSetting('user_phone', _phoneController.text);

        // Simulate API call delay
        await Future.delayed(const Duration(seconds: 1));

        AppHelper.showToast('Profile updated successfully');

        setState(() {
          _isEditing = false;
        });
      } catch (e) {
        AppHelper.showToast('Failed to update profile', isError: true);
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showComingSoon(String feature) {
    Get.snackbar(
      'Coming Soon',
      '$feature feature will be available in the next update',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
    );
  }
}
