import 'package:my_video/app_imports.dart';

class AuthenticationController extends GetxController {
  final AuthenticationRepository _authRepository =
      getIt<AuthenticationRepository>();
  final Logger _logger = Logger();

  // API call functions only
  Future<Map<String, dynamic>> loginFromAPI({
    required String email,
    required String password,
  }) async {
    try {
      return await _authRepository.login(email: email, password: password);
    } catch (e) {
      _logger.e('Error logging in from API: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> signupFromAPI({
    required String name,
    required String email,
    required String password,
    required String lastName,
    required String gender,
    File? profileImage,
  }) async {
    try {
      return await _authRepository.register(
        name: name,
        email: email,
        password: password,
        lastName: lastName,
        gender: gender,
        profileImage: profileImage,
      );
    } catch (e) {
      _logger.e('Error signing up from API: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> forgotPasswordFromAPI({
    required String email,
  }) async {
    try {
      return await _authRepository.forgotPassword(email: email);
    } catch (e) {
      _logger.e('Error sending forgot password request from API: $e');
      rethrow;
    }
  }

  Future<void> logoutFromAPI() async {
    try {
      await _authRepository.logout();
    } catch (e) {
      _logger.e('Error logging out from API: $e');
      rethrow;
    }
  }
}
